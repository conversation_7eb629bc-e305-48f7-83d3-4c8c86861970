package com.kidsup.giaoducsom.fragment.game.nhanbiet;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.github.florent37.viewanimator.AnimationListener;
import com.github.florent37.viewanimator.ViewAnimator;
import com.kidsup.giaoducsom.R;
import com.kidsup.giaoducsom.model.GameState;
import com.kidsup.giaoducsom.model.flashcard.Item;
import com.kidsup.giaoducsom.svg.Path;
import com.kidsup.giaoducsom.svg.SVGManager;
import com.kidsup.giaoducsom.utils.MyList;
import com.kidsup.giaoducsom.utils.Utils;
import com.kidsup.giaoducsom.utils.VisualTree;
import com.kidsup.giaoducsom.view.SVGAutosizeView;

public class DryGameFragment extends NhanBietGameFragment {
    SVGAutosizeView svg_view_1, svg_view_2, svg_view_3, svg_view, svg_view_drag;
    int viewsAdded = 0;
    float dX, dY;
    MyList<Item> items;
    Path[] pathsColor = new Path[3];
    Path[] shadowsColor = new Path[3];

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.game_nhanbiet_dry, container, false);
        buildTopPopupView(view);
        return view;
    }

    @Override
    protected void configureLayout(View view) {
        super.configureLayout(view);
        svg_view_1 = view.findViewById(R.id.svg_view_1);
        svg_view_2 = view.findViewById(R.id.svg_view_2);
        svg_view_3 = view.findViewById(R.id.svg_view_3);
        svg_view = view.findViewById(R.id.svg_view);
        svg_view_drag = view.findViewById(R.id.svg_view_drag);
        svg_view_drag.setOnTouchListener(new View.OnTouchListener() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        dX = svg_view_drag.getX() - event.getRawX();
                        dY = svg_view_drag.getY() - event.getRawY();
                        svg_view_drag.loadSVG("images/topics/" + folder + "/" + items.get(viewsAdded).path);
                        playSound(language + "/topics/" + folder + "/" + items.get(viewsAdded).path.replace(".svg", ""));
                        float scale = (float) svg_view_1.getWidth() / svg_view_drag.getWidth();
                        svg_view_drag.setScaleX(scale);
                        svg_view_drag.setScaleY(scale);
                        pathsColor[viewsAdded].setOpacity(0.01f);
                        shadowsColor[viewsAdded].setOpacity(0.001f);
                        svg_view.invalidate();
                        playSound("effect/cungchoi_pick" + random(1, 2));
                        Utils.vibrate(view.getContext());
                        break;
                    case MotionEvent.ACTION_MOVE:
                        float newX = event.getRawX() + dX;
                        float newY = event.getRawY() + dY;
                        svg_view_drag.setX(newX);
                        svg_view_drag.setY(newY);
                        break;
                    case MotionEvent.ACTION_UP:
                        Utils.vibrate(view.getContext());
                        SVGAutosizeView[] views = {svg_view_1, svg_view_2, svg_view_3};
                        float minDistance = Float.MAX_VALUE;
                        SVGAutosizeView minView = null;
                        for (int i = 0; i < views.length; i++) {
                            SVGAutosizeView view = views[i];
                            Point point = VisualTree.distanceCenterTwoViews(svg_view_drag, view);
                            float distance = (float) Math.sqrt(point.x * point.x + point.y * point.y);
                            if (distance < minDistance) {
                                minDistance = distance;
                                minView = view;
                            }
                        }
                        if (minDistance < minView.getWidth() / 2 && minView.getSvg() == null) {
                            playSound("effect/word puzzle drop");
                            SVGAutosizeView finalMinView = minView;
                            viewsAdded++;
                            long delay = playSound(viewsAdded == 3 ? "effect/answer_end" : "effect/answer_correct");
                            if (viewsAdded == 3) {
                                pauseGame();
                                animateCoinIfCorrect(svg_view_drag);
                                delay += playSound(getCorrectHumanSound(), EndGameSound());
                                scheduler.schedule(() -> {
                                    finishGame();
                                }, delay);
                            }
                            VisualTree.moveElement(svg_view_drag, minView).duration(200).onStop(new AnimationListener.Stop() {
                                @Override
                                public void onStop() {
                                    finalMinView.setSvg(svg_view_drag.getSvg());
                                    svg_view_drag.setSvg(null);
                                    svg_view_drag.setTranslationX(0);
                                    svg_view_drag.setTranslationY(0);
                                }
                            }).start();
                        } else {
                            playSound("effect/slide2");
                            setGameWrong();
                            float scaleX = (float) svg_view_1.getWidth() / svg_view_drag.getWidth();
                            ViewAnimator.animate(svg_view_drag).translationX(0).translationY(0).scale(scaleX).alpha(0.01f).duration(500).onStop(new AnimationListener.Stop() {
                                @Override
                                public void onStop() {
                                    svg_view_drag.setSvg(null);
                                    svg_view_drag.setAlpha(1);
                                    pathsColor[viewsAdded].setOpacity(1f);
                                    shadowsColor[viewsAdded].setOpacity(0.1f);
                                    svg_view.invalidate();
                                }
                            }).start();
                        }
                        break;
                }
                return true;
            }
        });
    }

    @Override
    protected void updateData() {
        super.updateData();
        items = new MyList<>(listItems).randomorder();
        long delay = playSound(OpenGameSound(), language + "/nhanbiet/nhanbiet_drying");
        scheduler.schedule(this::startGame, delay);
        SVGManager.getManager().loadSVG("images/nhanbiet_washing_machine.svg", svg -> {
            for (int i = 0; i < svg.pathCount(); i++) {
                Path path = svg.getPath(i);
                if ("change_x5F_color1".equals(path.getId())) {
                    path.setFillColor(Color.parseColor(items.get(0).theme_color));
                    pathsColor[0] = path;
                    shadowsColor[0] = svg.getPath(i + 1);
                }
                if ("change_x5F_color2".equals(path.getId())) {
                    path.setFillColor(Color.parseColor(items.get(1).theme_color));
                    pathsColor[1] = path;
                    shadowsColor[1] = svg.getPath(i + 1);
                }
                if ("change_x5F_color3".equals(path.getId())) {
                    path.setFillColor(Color.parseColor(items.get(2).theme_color));
                    pathsColor[2] = path;
                    shadowsColor[2] = svg.getPath(i + 1);
                }
            }
            svg_view.setSvg(svg);
        });
    }

    @Override
    public void replayIntroSound() {
        super.replayIntroSound();
        if (gameState == GameState.Playing) {
            pauseGame();
            long delay = playSound(language + "/nhanbiet/nhanbiet_drying");
            scheduler.schedule(this::resumeGame, delay);
        }
    }

    @Override
    protected void createGame() {
        super.createGame();
    }
}
