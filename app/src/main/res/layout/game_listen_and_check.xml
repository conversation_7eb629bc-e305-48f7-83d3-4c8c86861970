<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#7CD2FF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_sound"
        android:layout_width="0dp"
        android:layout_height="55dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/btn_sound_no_bg"
        android:visibility="gone"
        app:layout_constraintDimensionRatio="96:18"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginVertical="13dp"
            android:layout_marginRight="10dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="Listen and find"
            android:textColor="#99B6C1"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.78" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/image_sound"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="20dp"
        android:src="@drawable/btn_sound"
        app:layout_constraintBottom_toTopOf="@id/gridlayout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_sound" />

    <com.kidsup.giaoducsom.view.MyAutoColumnGridLayout
        android:id="@+id/gridlayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>