<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/lightblue">

    <View
        android:id="@+id/view_topleft"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHeight_percent="0.05"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_topright"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHeight_percent="0.05"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kidsup.giaoducsom.view.AnimatedImageButton
        android:id="@+id/btn_close"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0000"
        android:scaleType="fitXY"
        android:src="@drawable/btn_back"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintHorizontal_bias="0.05"
        app:layout_constraintLeft_toRightOf="@id/view_topleft"
        app:layout_constraintTop_toBottomOf="@id/view_topleft"
        app:layout_constraintVertical_bias="0.1" />

    <View
        android:id="@+id/view_topleft_2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHeight_percent="0.05"
        app:layout_constraintLeft_toRightOf="@id/btn_close"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0" />

    <TextView
        android:id="@+id/txt_title"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center_vertical|left"
        android:text="Tài khoản"
        android:textColor="@color/blue"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="@id/btn_close"
        app:layout_constraintHeight_percent="0.06"
        app:layout_constraintLeft_toRightOf="@id/view_topleft_2"
        app:layout_constraintRight_toRightOf="@id/view_center"
        app:layout_constraintTop_toTopOf="@id/btn_close" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btn_save"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/btn_save"
        android:scaleType="fitXY"
        android:visibility="visible"
        app:layout_constraintDimensionRatio="374:141"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintHorizontal_bias="0.05"
        app:layout_constraintRight_toLeftOf="@id/view_topright"
        app:layout_constraintTop_toBottomOf="@id/view_topright"
        app:layout_constraintVertical_bias="0.1">

        <TextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="Lưu"
            android:textColor="#fff"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/view_topright2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHeight_percent="0.05"
        app:layout_constraintRight_toLeftOf="@id/btn_save"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btn_remove"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/btn_remove"
        android:scaleType="fitXY"
        app:layout_constraintDimensionRatio="374:141"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintHorizontal_bias="0.05"
        app:layout_constraintRight_toLeftOf="@id/view_topright"
        app:layout_constraintTop_toBottomOf="@id/view_topright"
        app:layout_constraintVertical_bias="0.1">

        <TextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="Xóa"
            android:textColor="#fff"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/btn_close"
        app:layout_constraintRight_toRightOf="@id/btn_save"
        app:layout_constraintTop_toBottomOf="@id/btn_close">

        <com.kidsup.giaoducsom.view.PercentConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <com.kidsup.giaoducsom.view.PercentConstraintLayout
                android:id="@+id/info_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.45"
                app:pcl_layout_marginTopPercentPerWidth="0.05">

                <ImageView
                    android:id="@+id/image_avatar"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/text_name"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/text_name"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:ellipsize="start"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:singleLine="true"
                    android:text="xin chào"
                    android:textColor="#979ABD"
                    android:textSize="40dp"
                    app:heightRatio="0.4"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintLeft_toRightOf="@id/image_avatar"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:pcl_layout_marginLeftPercent="0.02" />

            </com.kidsup.giaoducsom.view.PercentConstraintLayout>

            <com.kidsup.giaoducsom.view.PercentConstraintLayout
                android:id="@+id/timer_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/info_view">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/timer_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/bg_set_timer0"
                    app:layout_constraintDimensionRatio="1.5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/info_view"
                    app:layout_constraintWidth_percent="0.45"
                    app:pcl_layout_marginTopPercentPerWidth="0.02">

                    <TextView
                        android:id="@+id/text_time"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:text="10 phút"
                        android:textColor="@color/blue"
                        app:autoSizeTextType="uniform"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="0.3"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.27"
                        app:layout_constraintWidth_percent="0.6" />

                    <com.kidsup.giaoducsom.view.AnimatedImageButton
                        android:id="@+id/btn_time_up"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/bg_set_timer1"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1"
                        app:layout_constraintHeight_percent="0.25"
                        app:layout_constraintHorizontal_bias="0.65"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.82" />

                    <com.kidsup.giaoducsom.view.AnimatedImageButton
                        android:id="@+id/btn_time_down"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/bg_set_timer2"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1"
                        app:layout_constraintHeight_percent="0.25"
                        app:layout_constraintHorizontal_bias="0.35"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.82" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:src="@drawable/bg_set_timer"
                    app:layout_constraintDimensionRatio="0.8"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/timer_view"
                    app:layout_constraintTop_toTopOf="@id/timer_view" />

                <TextView
                    android:id="@+id/text_setup_timer"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:lines="1"
                    android:text="Cài đặt thời gian học mỗi ngày"
                    android:textColor="@color/blue"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintDimensionRatio="6"
                    app:layout_constraintLeft_toLeftOf="@id/timer_view"
                    app:layout_constraintRight_toRightOf="@id/timer_view"
                    app:layout_constraintTop_toBottomOf="@id/timer_view" />

                <TextView
                    android:id="@+id/text_desc"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:lines="2"
                    android:text="Bé chỉ nên sử dụng trong 20 phút mỗi ngày! KidsUP sẽ thông báo khi bé hết giờ học."
                    android:textColor="#969ABC"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintDimensionRatio="15"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_setup_timer" />

            </com.kidsup.giaoducsom.view.PercentConstraintLayout>

            <View
                android:id="@+id/top_guide"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.05"></View>


            <TextView
                android:id="@+id/text_report"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/utm_avo_bold"
                android:text="Báo cáo học tập 7 ngày qua"
                android:textColor="@color/red"
                app:autoSizeTextType="uniform"
                app:layout_constraintDimensionRatio="6"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/timer_container"
                app:layout_constraintWidth_percent="0.6"
                app:pcl_layout_marginTopPercentPerWidth="0.05" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/thanhtich_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/baocao_thanhtich_bg"
                app:layout_constraintDimensionRatio="400:269.2"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_report"
                app:layout_constraintWidth_percent="0.47"

                app:pcl_layout_marginTopPercentPerWidth="0.05">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/line_center"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.45" />

                <TextView
                    android:id="@+id/text_lesson"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="123"
                    android:textColor="#00B7E8"
                    android:textSize="40dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/line_center"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.3" />

                <TextView
                    android:id="@+id/text_lesson_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text=" bài học"
                    android:textColor="#00B7E8"
                    android:textSize="20dp"
                    app:layout_constraintBottom_toBottomOf="@id/text_lesson"
                    app:layout_constraintHeight_percent="0.3"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintLeft_toRightOf="@id/text_lesson"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/text_lesson"
                    app:layout_constraintVertical_bias="0.3" />

                <TextView
                    android:id="@+id/text_coin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="123"
                    android:textColor="#FFB823"
                    android:textSize="40dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/line_center"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.6" />

                <TextView
                    android:id="@+id/text_coin_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text=" xu K"
                    android:textColor="#FFB823"
                    android:textSize="20dp"
                    app:layout_constraintBottom_toBottomOf="@id/text_coin"
                    app:layout_constraintHeight_percent="0.3"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintLeft_toRightOf="@id/text_coin"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/text_coin"
                    app:layout_constraintVertical_bias="0.3" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/bonus_container"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="400:350"
                    app:layout_constraintHeight_percent="0.3"
                    app:layout_constraintHorizontal_bias="0.6"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1">

                    <ImageView
                        android:id="@+id/image_bonus"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerInside"
                        android:src="@drawable/icon_bonus" />

                    <com.kidsup.giaoducsom.view.HeightRatioTextView
                        android:id="@+id/text_bonus_level"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="x2"
                        android:textColor="#fff"
                        app:heightRatio="0.8"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="0.3"
                        app:layout_constraintHorizontal_bias="0.8"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.2"
                        app:layout_constraintWidth_percent="0.5" />
                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/thanhthuc_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/baocao_thanhthuc_bg"
                app:layout_constraintDimensionRatio="400:269.2"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/thanhtich_container"
                app:layout_constraintWidth_percent="0.47">

                <com.caverock.androidsvg.SVGImageView
                    android:id="@+id/svg_image_game"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="737:559"
                    app:layout_constraintHeight_percent="0.45"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/text_skill"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="Đây là kỹ năng con đã thực hành nhiều nhất,hoàn thành 50 bài"
                    android:textColor="#969ABC"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHeight_percent="0.16"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.88"
                    app:layout_constraintWidth_percent="0.8" />
            </androidx.constraintlayout.widget.ConstraintLayout>


            <com.kidsup.giaoducsom.view.PercentConstraintLayout
                android:id="@+id/week_report_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/thanhtich_container">

                <TextView
                    android:id="@+id/text_subject_title"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/utm_avo_bold"
                    android:text="Tổng hợp điểm từng môn"
                    android:textColor="@color/red"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintDimensionRatio="6"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_percent="0.6"
                    app:pcl_layout_marginPercentPerWidth="0.05" />

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:src="@drawable/report_thang_cup"
                    app:layout_constraintDimensionRatio="400:400"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/subject_container"
                    app:layout_constraintTop_toTopOf="@id/subject_container" />

                <LinearLayout
                    android:id="@+id/subject_container"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="30dp"
                    android:background="#F2FDFF"
                    android:orientation="vertical"
                    android:padding="20dp"
                    app:layout_constraintHorizontal_bias="0.95"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text_subject_title"
                    app:layout_constraintWidth_percent="0.6">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="#0f0">

                        <include layout="@layout/item_report_7days" />
                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>
            </com.kidsup.giaoducsom.view.PercentConstraintLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintDimensionRatio="20"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/week_report_container" />
        </com.kidsup.giaoducsom.view.PercentConstraintLayout>
    </ScrollView>

    <View
        android:id="@+id/gradient_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/gradient_nhanbiet_list"
        app:layout_constraintDimensionRatio="30"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/scroll_view" />

</androidx.constraintlayout.widget.ConstraintLayout>