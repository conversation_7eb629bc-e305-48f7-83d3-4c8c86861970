<UserControl x:Class="projectname.Paths.dancing girl" d:DesignHeight="400" d:DesignWidth="400" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d" FontFamily="{StaticResource PhoneFontFamilyNormal}">
    <UserControl.Resources>
        <Storyboard x:Name="sb_stand" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="eye" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="0.1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.2000000" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:04" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
        <Storyboard x:Name="sb_dance" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="eye" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.ScaleY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:00.7000000" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="0.1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:00.9000000" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="leg_left" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-7">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="30">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="29.8921"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-7">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-7"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="leg_left" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateX)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-41">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-40.8805"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="0"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="leg_left" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-46">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-45.8659"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="0"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="hair" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-10">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-4">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-4.01749"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-10">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-10"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="hair" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateX)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-55">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="52">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="51.688"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-55">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-55"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="hair" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-14">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-17">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-16.9913"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-14">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-14"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="leg_right" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-30">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="7">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="6.89213"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-30">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-30"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="leg_right" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateX)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="39">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="0.113703"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="39">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="39"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="leg_right" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-49">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-0.142857"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-49">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-49"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="arm_left" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateX)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-24">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="39">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="38.8163"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-24">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-24"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="arm_left" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="28">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="102">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="101.784"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="28">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="28"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="arm_left" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-1">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-21">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-20.9417"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-1">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="arm_right" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-90">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-18">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-18.2099"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-90">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-90"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="arm_right" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateX)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-34">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="34">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="33.8018"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-34">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-34"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="arm_right" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-20">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="3">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="2.93294"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-20">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-20"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="body" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateX)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-15">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="16">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="15.9096"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-15">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-15"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="body" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-6">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="11">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="10.9504"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-6">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-6"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="body" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-4">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-6">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-5.99417"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-4">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-4"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="head" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-15">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="15">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="14.9125"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-15">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-15"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="head" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateX)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="-26">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="29">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="28.8397"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="-26">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="-26"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="head" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.TranslateY)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:00.8000000" Value="-3">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseInOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.1000000" Value="-2.99125"/>
                <EasingDoubleKeyFrame KeyTime="00:00:01.7000000" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:02" Value="0"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </UserControl.Resources>
    <Grid Width="400" Height="400">
        <Path x:Name="hair" Fill="#232323" Data="M274.7,163.7c0.1-0.1,0.1-0.3,0.2-0.4c5.9-12-0.3-23.7-4.1-35.3c-4.6-13.7-2.4-47-7.2-59.5&#xD;&#xA;	c-9-32.1-46.4-42.5-75.8-41.4c-31.5,1.1-58.5,11.3-67,39.7c-4.4,14.6-7.6,29.4-12,44c-3.6,11.8-12.2,23.8-10.5,36.5&#xD;&#xA;	c0.3,2,0.8,3.9,1.7,5.7c3.1,6.6,10,10.5,16.4,13.9c-2-3.1-2.6-7.1-1.5-10.6c4.2,5.5,10.5,9.1,17.2,11.2c6.6,2.1,13.6,2.8,20.5,3.5&#xD;&#xA;	c16.9,1.7,33.9,3.4,50.8,5c11.3,1.1,22.6,2.2,33.9,1.4c9-0.7,18.8-3.2,23.9-10.7c0,3.6-0.9,7.2-2.6,10.3&#xD;&#xA;	C265.4,175.1,271.4,170.1,274.7,163.7z" RenderTransformOrigin="0.493,0.191">
            <Path.RenderTransform>
                <CompositeTransform/>
            </Path.RenderTransform>
        </Path>
        <Grid x:Name="leg_left" RenderTransformOrigin="0.366,0.982">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Path Fill="#FCCEAE" Data="M163.9,381c-0.2,0.7-25.4-4.1-25.3-5c0.7-7.2,3.5-21.6,4-25.7l27,5.1C167.7,362.3,165.1,376.1,163.9,381&#xD;&#xA;		L163.9,381z">
            </Path>
            <Path Fill="#FFFFFF" Data="M165,384.5c-0.1,0.4-29.3-5.2-29.2-5.7c0.2-4.5,2.1-13.2,2.3-15.8l31.1,6&#xD;&#xA;		C167.9,373.6,165.9,381.6,165,384.5L165,384.5z">
            </Path>
            <Path Fill="#F9328D" Data="M133.3,371.7c0.6-1.4,1.7-3.2,2.6-4.9c0.7-1.2,13.2,0.6,12.8,3.7c-0.1,0.8-0.2,1.5-0.4,2.1&#xD;&#xA;		c2.1,0.3,4.5,0.6,6.9,0.8c5.4,0.5,10.2,0.7,13.6,0.6c-0.4,4.9-2,13.3-2.5,18.7c0,0-57.3,3.1-65.1-6.1&#xD;&#xA;		C99.2,384,114.5,375.7,133.3,371.7L133.3,371.7z">
            </Path>
            <Path Fill="#CC0E53" Data="M166.8,389.4c-0.2,1.2-0.3,2.4-0.4,3.4c0,0-57.3,3.1-65.1-6.1c-0.3-0.4-0.3-0.9,0.1-1.4&#xD;&#xA;		C116.4,390.6,152.6,389.9,166.8,389.4L166.8,389.4z">
            </Path>
            <Path Fill="#D3DAE5" Data="M136.4,376.8c0.1,0.2,0.4,0.4,0.6,0.4c0.2,0,0.3,0,0.5-0.1c0.4-0.2,0.5-0.7,0.3-1.1&#xD;&#xA;		c-1.8-3.2-5.4-5.2-8.7-4.9c-0.4,0-0.8,0.4-0.7,0.9c0,0.4,0.4,0.8,0.9,0.7C131.9,372.5,134.9,374.2,136.4,376.8L136.4,376.8z&#xD;&#xA;		 M132.2,378.2c0.1,0.2,0.4,0.3,0.6,0.3c0.2,0,0.4,0,0.5-0.1c0.4-0.3,0.4-0.7,0.2-1.1c-2.1-2.9-5.3-4.7-8.8-4.7&#xD;&#xA;		c-0.4,0-0.8,0.3-0.8,0.8c0,0.4,0.3,0.8,0.8,0.8C126.4,374.2,129.7,374.8,132.2,378.2L132.2,378.2z M119,375c0,0.4,0.4,0.8,0.8,0.8&#xD;&#xA;		c2.7-0.1,5.9,1.6,7.9,4.1c0.1,0.2,0.3,0.3,0.5,0.3c0.2,0,0.4,0,0.6-0.2c0.3-0.3,0.4-0.8,0.1-1.1c-2.3-2.9-6.1-4.8-9.2-4.7&#xD;&#xA;		C119.4,374.2,119,374.6,119,375L119,375z">
            </Path>
            <Path Fill="#224966" Data="M140.4,352.7C140.4,352.7,140.4,352.7,140.4,352.7C140.4,352.8,140.4,352.8,140.4,352.7z">
            </Path>
            <Path Fill="#224966" Data="M157.5,232.5c-4.3,12.1-16.9,118.5-17.1,120.2c9.3,1.7,24.4,4.5,31.8,4.7c3.4-20.7,16.3-67.7,18.8-78.3&#xD;&#xA;		c0.5-2.3,2.7-3.1,4.4-4.2c2.2-1.3,3.9-3,5.1-5.4c1.3-2.7,1.9-5.7,2.3-8.6c0.6-3.6,1.1-7.2,0.6-10.8c-0.4-3.6-1.8-7.2-3.9-10.1&#xD;&#xA;		c-4.5-6.2-14.4-4.4-21.2-4.9C171.4,234.7,164.3,234.3,157.5,232.5z">
            </Path>
            <Path Fill="#001D26" Data="M156.4,237.6c-0.8,6.3-4.6,25.3-4.7,28c7.6,0.5,14.3-4.9,15.3-12.5C167.8,245.9,163.2,239.4,156.4,237.6&#xD;&#xA;		L156.4,237.6z">
            </Path>
            <Path Fill="#3F8CB2" Data="M172.6,363.7l-35-6.5l2.1-11.4l35,6.5L172.6,363.7z">
            </Path>
        </Grid>
        <Grid x:Name="leg_right" RenderTransformOrigin="0.622,0.982">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Path Fill="#FCCEAE" Data="M224.9,381c-1.1-4.8-3.8-18.6-5.6-25.6l27-5.1c0.5,4.2,3.2,18.5,4,25.7C250.3,376.8,225,381.6,224.9,381&#xD;&#xA;		L224.9,381z">
            </Path>
            <Path Fill="#FFFFFF" Data="M223.8,384.5c-0.9-2.9-2.8-10.9-4.2-15.5l31.1-6c0.2,2.6,2.1,11.3,2.3,15.8&#xD;&#xA;		C253,379.3,223.9,384.9,223.8,384.5L223.8,384.5z">
            </Path>
            <Path Fill="#F9328D" Data="M255.5,371.7c-0.6-1.4-1.7-3.2-2.6-4.9c-0.7-1.2-13.2,0.6-12.8,3.7c0.1,0.8,0.2,1.5,0.4,2.1&#xD;&#xA;		c-2.1,0.3-4.5,0.6-6.9,0.8c-5.4,0.5-10.2,0.7-13.6,0.6c0.4,4.9,2,13.3,2.5,18.7c0,0,57.3,3.1,65.1-6.1&#xD;&#xA;		C289.6,384,274.3,375.7,255.5,371.7L255.5,371.7z">
            </Path>
            <Path Fill="#CC0E53" Data="M222,389.4c0.2,1.2,0.3,2.4,0.4,3.4c0,0,57.3,3.1,65.1-6.1c0.3-0.4,0.3-0.9-0.1-1.4&#xD;&#xA;		C272.4,390.6,236.2,389.9,222,389.4L222,389.4z">
            </Path>
            <Path Fill="#D3DAE5" Data="M252.4,376.8c1.5-2.6,4.5-4.3,7.2-4.1c0.4,0,0.8-0.3,0.9-0.7c0-0.4-0.3-0.8-0.7-0.9&#xD;&#xA;		c-3.3-0.3-6.9,1.7-8.7,4.9c-0.2,0.4-0.1,0.9,0.3,1.1c0.1,0.1,0.3,0.1,0.5,0.1C252.1,377.2,252.3,377.1,252.4,376.8L252.4,376.8z&#xD;&#xA;		 M256.6,378.2c2.5-3.5,5.8-4,7.6-4.1c0.4,0,0.8-0.4,0.8-0.8c0-0.4-0.4-0.8-0.8-0.8c-3.5,0-6.7,1.8-8.8,4.7&#xD;&#xA;		c-0.3,0.4-0.2,0.8,0.2,1.1c0.2,0.1,0.4,0.2,0.5,0.1C256.3,378.5,256.5,378.4,256.6,378.2L256.6,378.2z M269.8,375&#xD;&#xA;		c0-0.4-0.3-0.8-0.8-0.8c-3.2-0.1-6.9,1.8-9.2,4.7c-0.3,0.3-0.2,0.8,0.1,1.1c0.2,0.1,0.4,0.2,0.6,0.2c0.2,0,0.4-0.1,0.5-0.3&#xD;&#xA;		c2-2.5,5.3-4.2,7.9-4.1C269.4,375.8,269.7,375.4,269.8,375L269.8,375z">
            </Path>
            <Path Fill="#224966" Data="M248.4,352.7C248.4,352.7,248.4,352.7,248.4,352.7C248.4,352.8,248.4,352.8,248.4,352.7z">
            </Path>
            <Path Fill="#224966" Data="M231.3,232.5c4.3,12.1,16.9,118.5,17.1,120.2c-9.3,1.7-24.4,4.5-31.8,4.7c-3.4-20.7-16.3-67.7-18.8-78.3&#xD;&#xA;		c-0.5-2.3-2.7-3.1-4.4-4.2c-2.2-1.3-3.9-3-5.1-5.4c-1.3-2.7-1.9-5.7-2.3-8.6c-0.6-3.6-1.1-7.2-0.6-10.8c0.4-3.6,1.8-7.2,3.9-10.1&#xD;&#xA;		c4.5-6.2,14.4-4.4,21.2-4.9C217.4,234.7,224.5,234.3,231.3,232.5z">
            </Path>
            <Path Fill="#001D26" Data="M232.4,237.6c0.8,6.3,4.6,25.3,4.7,28c-7.6,0.5-14.3-4.9-15.3-12.5C221,245.9,225.6,239.4,232.4,237.6&#xD;&#xA;		L232.4,237.6z">
            </Path>
            <Path Fill="#3F8CB2" Data="M214.2,352.3l35-6.5l2.1,11.4l-35,6.5L214.2,352.3z">
            </Path>
        </Grid>
        <Grid x:Name="arm_left" RenderTransformOrigin="0.389,0.458">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Path Fill="#FCCEAE" Data="M129.3,248.6c-1.1,1.7-12.7-5.3-11.7-7.1c14.9-25.7,31.6-58.8,34.6-65.8l18.1,10.2&#xD;&#xA;		C161.5,200.6,129.3,248.6,129.3,248.6z">
            </Path>
            <Path Fill="#A93CD6" Data="M119.4,235.5c7,3.4,14.5,8.1,20.6,11.7c4.9-5.8,40.3-57.6,40.6-58.5c10.5-25.8-25-19-29.7-12.4&#xD;&#xA;		C144.1,186.2,125.9,222.8,119.4,235.5L119.4,235.5z">
            </Path>
            <Path Fill="#E0956C" Data="M108.5,275.7c2-4,4.3-10.3,10.4-21.4c0.7-1.3,7-0.7,6.1,0.5C119.8,261.7,114.5,277.5,108.5,275.7&#xD;&#xA;		L108.5,275.7z">
            </Path>
            <Path Fill="#E0956C" Data="M101.8,275.6c2-4,4.9-14.5,13.4-23.3c1.1-1.1,7,0.2,6.1,1.4C116.2,260.6,106.9,277,101.8,275.6L101.8,275.6&#xD;&#xA;		z">
            </Path>
            <Path Fill="#FCCEAE" Data="M94.8,272.1c2-4,16.3-24,24.8-32.8c1.1-1.1,9.6,5.4,9.5,7.8c2.6,5-3,12.5-2.5,24.3c0.1,2-2.8,0.7-3.4,0&#xD;&#xA;		c-1.7-1.8-1.4-2.7-3.8-8.2c-1.5-3.4-5.6-3.1-7.7-1.9C103.3,267.7,98.8,273.6,94.8,272.1L94.8,272.1z">
            </Path>
        </Grid>
        <Grid x:Name="arm_right" RenderTransformOrigin="0.584,0.46">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Path Fill="#FCCEAE" Data="M259.5,248.6c1.1,1.7,12.7-5.3,11.7-7.1c-14.9-25.7-31.6-58.8-34.6-65.8l-18.1,10.2&#xD;&#xA;		C227.2,200.6,259.5,248.6,259.5,248.6z">
            </Path>
            <Path Fill="#A93CD6" Data="M269.4,235.5c-7,3.4-14.5,8.1-20.6,11.7c-4.9-5.8-40.3-57.6-40.6-58.5c-10.5-25.8,25-19,29.7-12.4&#xD;&#xA;		C244.7,186.2,262.8,222.8,269.4,235.5L269.4,235.5z">
            </Path>
            <Path Fill="#E0956C" Data="M280.3,275.7c-2-4-4.3-10.3-10.4-21.4c-0.7-1.3-7-0.7-6.1,0.5C268.9,261.7,274.2,277.5,280.3,275.7&#xD;&#xA;		L280.3,275.7z">
            </Path>
            <Path Fill="#E0956C" Data="M286.9,275.6c-2-4-4.9-14.5-13.4-23.3c-1.1-1.1-7,0.2-6.1,1.4C272.5,260.6,281.9,277,286.9,275.6&#xD;&#xA;		L286.9,275.6z">
            </Path>
            <Path Fill="#FCCEAE" Data="M294,272.1c-2-4-16.3-24-24.8-32.8c-1.1-1.1-9.6,5.4-9.5,7.8c-2.6,5,3,12.5,2.5,24.3c-0.1,2,2.8,0.7,3.4,0&#xD;&#xA;		c1.7-1.8,1.4-2.7,3.8-8.2c1.5-3.4,5.6-3.1,7.7-1.9C285.5,267.7,289.9,273.6,294,272.1L294,272.1z">
            </Path>
        </Grid>
        <Grid x:Name="body" RenderTransformOrigin="0.487,0.67">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Path Fill="#7819A5" Data="M218,174.9c-0.1,0.3-0.3,0.5-0.5,0.8c-0.5,0.6-1.3,0.9-2.1,1.2c-6.6,2.4-13.9,2.1-20.9,1.8&#xD;&#xA;		c-2.2-0.1-4.4-0.2-6.6-0.3c-2.8-0.1-5.7-0.3-8.5-0.7c-2.1-0.4-5.3-0.1-6.8-1.8c-0.7-0.8-0.4-1.8-0.4-2.7c-0.1-2.5-0.3-5,0.3-7.4&#xD;&#xA;		c0.2-0.8,0.6-1.6,1.3-2.1c1.4-0.9,4.1-0.4,5.8-0.4c10.7-0.3,21.3-0.3,31.9-0.3c1.1,0,2.3,0,3.2,0.7c0.5,0.4,0.8,0.9,1.1,1.4&#xD;&#xA;		c1.2,2.1,2,4.4,2.3,6.8C218.4,172.9,218.4,174,218,174.9z">
            </Path>
            <Path Fill="#E476FF" Data="M239.4,259c-1.3-23.7-1.3-83.3-12.1-86.1c-27.7-7.1-53.6-3.5-68-0.1c-1.3,19.2-7.9,69.5-10.1,88.3&#xD;&#xA;		C182.2,271.5,227.9,269.9,239.4,259L239.4,259z">
            </Path>
            <Path Fill="#FCCEAE" Data="M213.8,175.1c0.1-1.9-0.3-3.3-1.2-5c-11.8-0.5-26-1-37-0.4c-0.3,0.9,0.1,2,0,3c-0.5,7.8,7.7,14.6,18.2,15.2&#xD;&#xA;		C204.4,188.6,213.4,182.9,213.8,175.1L213.8,175.1z">
            </Path>
            <Path Fill="#A93CD6" Data="M172.2,170.9c1.1-3.6-14.3,0-19.9,4.3c-0.2,22.1-1.4,35.6-3.6,61.9c7.6,1.3,26.5,2.4,31.1,1.9&#xD;&#xA;		c0.2-7.6-1.6-38.9-2.6-56.2C172.7,178.7,172.2,171.2,172.2,170.9L172.2,170.9z">
            </Path>
            <Path Fill="#FCCEAE" Data="M210,148.5l-0.7,26c-0.2,5.6-7.4,9.9-16.1,9.7c-8.8-0.3-15.7-5-15.6-10.6l0.7-26L210,148.5z">
            </Path>
            <Path Opacity="0.2" Data="M164.5,187.3c0.5-3.7,1.8-13.8,2.8-15.9c0,0,5.1-4.2,5.5-4.5c-2.3,3.5,1,8.9,4.4,15.9&#xD;&#xA;		C174.7,181.7,168,185.4,164.5,187.3L164.5,187.3z" Fill="#000000">
            </Path>
            <Path Fill="#7819A5" Data="M177.3,182.8l-6.6-6.6l1.5-2.8c0,0,3,3.5,4.3,5.1C177.1,179.3,177.3,182.8,177.3,182.8L177.3,182.8z">
            </Path>
            <Path Fill="#A93CD6" Data="M166.2,185.4c-0.1-3.2,0.2-10.7,1-13.8c0,0,6.7-8.1,7.1-8.5c-1.9,3.6,0,12.3,2.1,15.5&#xD;&#xA;		C173.8,177.4,168.6,183.5,166.2,185.4L166.2,185.4z">
            </Path>
            <Path Opacity="0.2" Fill="#A31414" Data="M209.7,164.9c0-1.1-0.2-2.1-0.4-3.1c-8.8-0.7-22-0.8-31.3,0.3c-0.1,0.5-0.2,0.9-0.2,1.4&#xD;&#xA;		c-0.1,8.3,7,15.4,15.9,15.8C202.4,179.7,209.6,173.2,209.7,164.9L209.7,164.9z">
            </Path>
            <Path Fill="#7819A5" Data="M151.5,204.1c0-0.7,0.1-1.3,0.1-2c6.3-0.3,15.9-1,26.6-1.7c0,0.6,0.1,1.3,0.1,2&#xD;&#xA;		C167.5,203.1,157.7,203.7,151.5,204.1L151.5,204.1z">
            </Path>
            <Path Fill="#661496" Data="M175.6,188.7c0,0.5-1,1-2.3,1c-1.3,0-2.3-0.3-2.3-0.9c0-0.5,1-1,2.3-1C174.6,187.8,175.6,188.2,175.6,188.7&#xD;&#xA;		L175.6,188.7z">
            </Path>
            <Path Fill="#661496" Data="M176.2,207.1c0,0.5-1,1-2.3,1c-1.3,0-2.3-0.3-2.3-0.9s1-1,2.3-1C175.1,206.2,176.1,206.6,176.2,207.1&#xD;&#xA;		L176.2,207.1z">
            </Path>
            <Path Fill="#661496" Data="M176.6,223.8c0,0.5-1,1-2.3,1c-1.3,0-2.3-0.3-2.3-0.9c0-0.5,1-1,2.3-1C175.6,222.9,176.6,223.3,176.6,223.8&#xD;&#xA;		L176.6,223.8z">
            </Path>
            <Path Fill="#A93CD6" Data="M216.6,170.9c8,0,13.7,1.2,19.9,4.3c0.2,22.1,1.4,35.6,3.6,61.9c-7.6,1.3-26.5,2.4-31.1,1.9&#xD;&#xA;		c-0.2-7.6,1.6-38.9,2.6-56.2C216.1,178.7,216.6,171.2,216.6,170.9L216.6,170.9z">
            </Path>
            <Path Opacity="0.2" Data="M224.3,187.3c-0.5-3.7-1.8-13.8-2.8-15.9c0,0-5.1-4.2-5.5-4.5c2.3,3.5-1,8.9-4.4,15.9&#xD;&#xA;		C214.1,181.7,220.8,185.4,224.3,187.3L224.3,187.3z" Fill="#000000">
            </Path>
            <Path Fill="#7819A5" Data="M211.6,182.8l6.6-6.6l-1.5-2.8c0,0-3,3.5-4.3,5.1C211.7,179.3,211.6,182.8,211.6,182.8L211.6,182.8z">
            </Path>
            <Path Fill="#A93CD6" Data="M222.7,185.4c0.1-3.2-0.2-10.7-1-13.8c0,0-6.7-8.1-7.1-8.5c1.9,3.6,0,12.3-2.1,15.5&#xD;&#xA;		C215,177.4,220.2,183.5,222.7,185.4L222.7,185.4z">
            </Path>
            <Path Fill="#7819A5" Data="M237.4,204.1c-6.3-0.3-16-1-26.8-1.7c0-0.7,0.1-1.3,0.1-2c10.7,0.8,20.3,1.4,26.6,1.7&#xD;&#xA;		C237.3,202.8,237.3,203.4,237.4,204.1L237.4,204.1z">
            </Path>
            <Path Fill="#661496" Data="M213.2,188.7c0-0.5,1.1-0.9,2.3-0.9c1.3,0,2.3,0.5,2.3,1c0,0.5-1.1,0.9-2.3,0.9&#xD;&#xA;		C214.2,189.7,213.2,189.2,213.2,188.7L213.2,188.7z">
            </Path>
            <Path Fill="#661496" Data="M212.7,207.1c0-0.5,1.1-0.9,2.3-0.9c1.3,0,2.3,0.5,2.3,1s-1.1,0.9-2.3,0.9&#xD;&#xA;		C213.7,208.1,212.6,207.6,212.7,207.1L212.7,207.1z">
            </Path>
            <Path Fill="#661496" Data="M212.2,223.8c0-0.5,1.1-0.9,2.3-0.9c1.3,0,2.3,0.5,2.3,1c0,0.5-1.1,0.9-2.3,0.9&#xD;&#xA;		C213.2,224.8,212.2,224.3,212.2,223.8L212.2,223.8z">
            </Path>
        </Grid>
        <Grid x:Name="head" RenderTransformOrigin="0.492,0.419">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Grid>
                <Path Fill="#28AFC6" Data="M257,93.4c3.4-23.4-2.4-45-16-59.2c-12.9-13.6-31.7-19.7-54.5-17.8c-0.4,0-0.9,0.1-1.3,0.1l-0.4-4.7&#xD;&#xA;			c24.9-2.3,45.4,4.3,59.7,19.2c14.6,15.3,20.9,38.3,17.3,63.1L257,93.4z">
                </Path>
                <Path Fill="#1C839B" Data="M265.1,121.5c-1,14.5-3,26.1-4.4,26c-1.4-0.1-1.7-11.9-0.6-26.4c1-14.5,3-26.1,4.4-26&#xD;&#xA;			C265.9,95.2,266.1,107.1,265.1,121.5L265.1,121.5z">
                </Path>
                <Path Fill="#1C839B" Data="M260.7,147.5l-12.3-0.9l3.8-52.4l12.3,0.9L260.7,147.5z">
                </Path>
                <Path Fill="#FFFFFF" Data="M253.2,120.7c-1,14.5-2.9,26.1-4.3,26c-1.3-0.1-1.5-11.9-0.5-26.4c1-14.5,2.9-26.1,4.3-26&#xD;&#xA;			C254.1,94.4,254.3,106.2,253.2,120.7L253.2,120.7z">
                </Path>
                <Path Fill="#28AFC6" Data="M252.7,120.6c-1,14.5-2.9,26.1-4.3,26c-1.3-0.1-1.5-11.9-0.5-26.4c1-14.5,2.9-26.1,4.3-26&#xD;&#xA;			C253.5,94.4,253.7,106.2,252.7,120.6L252.7,120.6z">
                </Path>
                <Path Fill="#FFFFFF" Data="M249.4,132.6c-0.6,0-0.7-5.4-0.2-12.2c0.5-6.8,1.4-12.1,2-12.1c0.6,0,0.7,5.4,0.2,12.2&#xD;&#xA;			C250.9,127.4,250.1,132.7,249.4,132.6L249.4,132.6z M251.1,109.6c-0.5,0-1.2,4.9-1.7,10.8c-0.4,5.9-0.4,10.9,0.1,10.9&#xD;&#xA;			c0.5,0,1.2-4.9,1.7-10.8C251.6,114.6,251.6,109.7,251.1,109.6L251.1,109.6z">
                </Path>
                <Path Fill="#F4B18C" Data="M249.6,116.9c-0.7,13.4-5.4,23.2-9.5,23.2c-4,0-5.4-10.4-5.4-23c0-12.7,3.2-23,7.2-23&#xD;&#xA;			C247.5,94.2,250.2,104.4,249.6,116.9L249.6,116.9z">
                </Path>
                <Path Fill="#232323" Data="M123,72c4.5-38,34-52.9,74.2-52.7c37.7,0.1,60.3,22.7,62.3,49.2c3.5,47.8-25.6,81.7-65.1,81.3&#xD;&#xA;			C155,149.4,117.3,121,123,72L123,72z">
                </Path>
                <Path Fill="#FCCEAE" Data="M142.6,60.6c9.5-20.6,25.7-20.6,52.4-20.3c28.6,0.3,50.4,1.4,58.7,26.6c2.4,7.5-1,68-9,81.4&#xD;&#xA;			c-6.8,11.3-33,20.6-40.9,20.5c-7.4-0.1-43.6-9.1-52.1-19.6C143.7,139.3,139,68.6,142.6,60.6L142.6,60.6z">
                </Path>
                <Path Fill="#FCCEAE" Data="M121,126.2c-3.3-12.8,2.7-27.4,17.7-24.4c17.9,3.6,18.9,44.1,9,45.4C137.7,148.5,124.5,139.9,121,126.2&#xD;&#xA;			L121,126.2z">
                </Path>
                <Path Fill="#E0956C" Data="M132.1,125.7c-0.7-5.8,2.7-11.7,7.9-11.6c3,0,6.1,20.1,4.2,20.7C139.2,136.4,132.8,131.5,132.1,125.7&#xD;&#xA;			L132.1,125.7z">
                </Path>
                <Path Fill="#FF9797" Data="M153.3,133.9c0.1-2.9,4.2-5,9.1-4.8c4.9,0.2,8.8,2.8,8.6,5.6c-0.1,2.9-4.2,5-9.1,4.8&#xD;&#xA;			C157,139.3,153.1,136.8,153.3,133.9L153.3,133.9z">
                </Path>
                <Path Fill="#FF9797" Data="M229.7,134.1c-0.1-2.5,2.9-4.8,6.7-5c3.8-0.2,7,1.7,7.1,4.3c0.1,2.5-2.9,4.8-6.7,5&#xD;&#xA;			C233.1,138.6,229.9,136.7,229.7,134.1L229.7,134.1z">
                </Path>
                <Path Fill="#701608" Data="M224.6,135.3c1.7,9-9.7,18.7-21.6,18.7l-0.2,0c-11.9-0.1-23.1-10-21.3-19c0.9-4.7,8.1-3.4,19.8-3.4&#xD;&#xA;			c0.6,0,3,0,3.6,0C216.6,131.8,223.8,130.6,224.6,135.3L224.6,135.3z">
                </Path>
                <Path Fill="#DD6B5B" Data="M203,154.1l-0.2,0c-3.9,0-7.9-1.2-11.6-3.2l-0.2-0.1l0.2-0.2c3.1-2.7,7.3-4.2,11.8-4.2&#xD;&#xA;			c4.5,0,8.6,1.6,11.7,4.4l0.2,0.2l-0.2,0.1C210.9,153.1,206.9,154.2,203,154.1L203,154.1z">
                </Path>
                <Path Fill="#FFFFFF" Data="M203,137.6c-8.2-0.1-15-2.7-15.8-6l-0.1-0.2l31.8,0.2l-0.1,0.2C218,135.2,211.2,137.6,203,137.6L203,137.6&#xD;&#xA;			z">
                </Path>
                <Path Fill="#C4735B" Data="M199.1,125.2c-0.4-0.1-0.6-0.4-0.5-0.8c0.5-2.9,2.9-4.8,5.9-4.8c3,0,5.4,2.1,5.8,4.9&#xD;&#xA;			c0.1,0.4-0.2,0.7-0.6,0.7c-0.3,0-0.7-0.2-0.7-0.6c-0.3-2.2-2.1-3.8-4.6-3.8c-2.4,0-4.2,1.4-4.6,3.7c-0.1,0.3-0.3,0.5-0.7,0.5&#xD;&#xA;			L199.1,125.2z">
                </Path>
                <Path Fill="#232323" Data="M132.4,72.6c-4.7-1.3,5.1-35.4,28.9-45.1c38.9-12.8,65.4-5,75.2,1.4c18,11.7,25.7,23.4,28.1,48.7&#xD;&#xA;			c1.4,15-0.1,19.9,7.5,23.6c-8.4,2-8.6,0.4-10.8-3.7c-1.3,12.1-0.2,17.6,5.6,21.9c-10.3,2.3-14.1-4.9-14.3-14.6&#xD;&#xA;			c-0.2-11.6,2.6-33.4,1.2-37.5c-0.8-2.4-12.7-16.1-16.2-16.4c-7.8-0.6-17.5,26.3-45.1,24.6c11.4-5.5,17.8-13.8,24.5-25.7&#xD;&#xA;			c-2.2,1.2-4.5,2.7-6.7,4.5c-23.5,19.2-44.6,23.3-63.9,20.7c2.2,20.8,3.9,47.1-18.1,47.1c2.4-2.7,8.6-16.3,3.2-27.4&#xD;&#xA;			C128.7,96.2,131.3,86.2,132.4,72.6L132.4,72.6z">
                </Path>
                <Path Fill="#F9328D" Data="M128.7,79.7c5.4-33.5,24.2-54.4,61.5-59.6c26.6-3.7,52.1,6.9,57.8,17.8c-8.1-6.4-22.1-14.6-59.4-7.7&#xD;&#xA;			C170.6,33.5,143.1,41.8,128.7,79.7L128.7,79.7z">
                </Path>
                <Path Fill="#E0956C" Data="M118,123.9c-1.2-6.1,2-12.6,7.4-12.9c3.2-0.2,8,20.9,6,21.7C126.4,134.6,119.2,129.9,118,123.9L118,123.9z&#xD;&#xA;			">
                </Path>
                <Path Fill="#28AFC6" Data="M127.9,100.5c-5.8-30.5-3.4-51.4,7.5-66c9.7-13,25.9-20.4,49.4-22.7l0.4,4.7c-22.1,2.1-37.2,8.9-46.1,20.8&#xD;&#xA;			c-10.1,13.5-12.2,33.3-6.6,62.3L127.9,100.5L127.9,100.5z">
                </Path>
                <Path Fill="#1C839B" Data="M154,120.5c2.4,14.3-1.8,26.9-9.3,28.2c-7.6,1.3-15.7-9.3-18.1-23.6c-2.4-14.3,1.7-26.9,9.3-28.1&#xD;&#xA;			C143.4,95.6,151.6,106.2,154,120.5L154,120.5z">
                </Path>
                <Path Fill="#1C839B" Data="M144.5,148.7l-17.2,2.8l-8.7-51.8l17.3-2.9L144.5,148.7z">
                </Path>
                <Path Fill="#FFFFFF" Data="M139,123c2.4,14.3-1.4,26.9-8.6,28c-7.2,1.2-15-9.4-17.4-23.7c-2.4-14.3,1.4-26.9,8.6-28&#xD;&#xA;			C128.8,98,136.5,108.7,139,123L139,123z">
                </Path>
                <Path Fill="#28AFC6" Data="M136,123.5c2.4,14.3-1.5,26.9-8.6,28c-7.2,1.2-15-9.5-17.4-23.7c-2.4-14.3,1.4-26.9,8.6-28&#xD;&#xA;			C125.8,98.5,133.5,109.2,136,123.5L136,123.5z">
                </Path>
                <Path Fill="#FFFFFF" Data="M125,137.6c-3.3,0.5-6.9-4.3-8-11c-1.1-6.7,0.6-12.4,3.9-13c3.3-0.6,6.8,4.3,8,11&#xD;&#xA;			C130.1,131.4,128.3,137.1,125,137.6L125,137.6z M121.2,114.9c-2.6,0.4-4,5.7-3,11.5c1,5.8,4,10.4,6.7,9.9c2.6-0.4,4-5.7,3-11.5&#xD;&#xA;			C126.8,119,123.8,114.5,121.2,114.9L121.2,114.9z">
                </Path>
                <Path Fill="#FF4D96" Data="M148.2,39.6c1.5-6.3,8.8-12,12.6-11c7.2,1.9,11.7,11.6,9.8,13.3c-3.2,2.7-12.5,3-18.9,3.1&#xD;&#xA;			C148.7,45,147.5,42.8,148.2,39.6z">
                </Path>
                <Path Fill="#FF4D96" Data="M149.2,47.7c-2.6,10-5.6,16.6-8.8,17.1c-5.1,0.9-15.1-7.1-13.8-12.6c1.3-5.6,13.4-11.2,18.8-10&#xD;&#xA;			C148.8,42.9,150,44.5,149.2,47.7z">
                </Path>
                <Path Fill="#CC0E53" Data="M150.4,41.8c0.7-1.8,3.5-6.6,5.5-3.9C158.4,41.4,152.2,42.1,150.4,41.8z">
                </Path>
                <Path Fill="#CC0E53" Data="M140.9,49.5c-3.4-2.7,3.4-4.5,5.6-4.9C146.1,46.7,144.4,52.3,140.9,49.5z">
                </Path>
                <Path Fill="#FF4D96" Data="M151.4,41.8c1.2,1.8,1,4.1-0.4,5.1c-1.4,1-3.6,0.3-4.8-1.5c-1.2-1.8-1-4.1,0.4-5.1&#xD;&#xA;			C148,39.3,150.1,40,151.4,41.8z">
                </Path>
                <Path Fill="#1E1E1E" Data="M252.8,86.7c0,1.1-5.2-3.6-11.6-5.2c-6.5-1.7-13.3-0.2-14.4-0.6c-0.8-0.6-0.9-4.6,0.5-5.5&#xD;&#xA;			c1.2-1,9.5-0.8,14.2,1.5C246.1,79.1,253.1,85.6,252.8,86.7z">
                </Path>
                <Path Fill="#1E1E1E" Data="M164.5,75.3c5.3-2.6,14.5-2.6,15.9-1.5c1.4,1,1.4,5.3,0.4,6.1c-1.3,0.3-8.9-1.4-16.3,0.4&#xD;&#xA;			c-7.3,1.7-13.1,6.6-13.2,5.8C151.4,84.8,159.1,77.6,164.5,75.3z">
                </Path>
            </Grid>
            <Grid x:Name="eye" RenderTransformOrigin="0.51,0.287">
                <Grid.RenderTransform>
                    <CompositeTransform/>
                </Grid.RenderTransform>
                <Path Fill="#F9FEFF" Data="M218.9,121.6c-2.3-15.8,4.2-23.1,11.3-24.9c7.7-1.9,19.6,8.3,11.8,21C235.7,128.1,218.9,121.6,218.9,121.6&#xD;&#xA;			L218.9,121.6z">
                </Path>
                <Path Fill="#1E1E1E" Data="M243.4,101.4c3.2,4.8,1.9,13.4-1.5,16.6c2.9-4.7,3.1-10.7,0.8-14.8c-2.5-4.6-8.1-6.9-12.3-5.8&#xD;&#xA;			c-2.7,0.7-7,2.3-9.5,6.8c1.3-4,6-7.9,10.7-8.4C236.1,95.3,240.3,96.8,243.4,101.4L243.4,101.4z">
                </Path>
                <Path Fill="#1E1E1E" Data="M223.3,108.1c1.5-4.6,5.9-6.8,9.8-4.9c3.9,1.9,5.7,7.1,4.2,11.6c-1.5,4.6-5.9,6.8-9.8,4.9&#xD;&#xA;			C223.6,117.8,221.8,112.6,223.3,108.1L223.3,108.1z">
                </Path>
                <Path Fill="#FEFFFE" Data="M223.9,106.6c0.4-1.3,1.6-1.9,2.7-1.4c1.1,0.5,1.6,2,1.2,3.2c-0.4,1.3-1.6,1.9-2.7,1.4&#xD;&#xA;			C224,109.4,223.5,107.9,223.9,106.6L223.9,106.6z">
                </Path>
                <Path Fill="#F9FEFF" Data="M189,121.6c0,0-19.9,6.2-27.4-4.2c-9.2-12.8,5-22.8,14.2-20.8C184.2,98.4,191.9,105.8,189,121.6L189,121.6&#xD;&#xA;			z">
                </Path>
                <Path Fill="#1E1E1E" Data="M160.2,101.1c3.7-4.6,8.7-6.1,14-5.5c5.5,0.6,11,4.5,12.6,8.5c-2.9-4.5-8-6.2-11.2-6.9&#xD;&#xA;			c-5-1.1-11.7,1.1-14.7,5.7c-2.7,4.1-2.5,10.1,0.9,14.8C157.8,114.5,156.3,105.9,160.2,101.1L160.2,101.1z">
                </Path>
                <Path Fill="#1E1E1E" Data="M165.2,107.7c1.8-4.6,7-6.8,11.6-4.9c4.6,1.9,6.8,7,4.9,11.6c-1.8,4.6-7,6.8-11.6,4.9&#xD;&#xA;			C165.5,117.5,163.3,112.3,165.2,107.7L165.2,107.7z">
                </Path>
                <Path Fill="#FEFFFE" Data="M165.9,106.3c0.5-1.3,2-1.9,3.2-1.4c1.3,0.5,1.9,2,1.4,3.2c-0.5,1.3-2,1.9-3.2,1.4&#xD;&#xA;			C166,109,165.4,107.6,165.9,106.3L165.9,106.3z" RenderTransformOrigin="0.487,0.688">
                </Path>
            </Grid>
        </Grid>
    </Grid>
</UserControl>