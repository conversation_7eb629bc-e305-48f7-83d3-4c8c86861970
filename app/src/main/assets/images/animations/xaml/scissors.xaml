<UserControl x:Class="projectname.Paths.scissors" d:DesignHeight="400" d:DesignWidth="400" xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d" FontFamily="{StaticResource PhoneFontFamilyNormal}">
    <UserControl.Resources>
        <Storyboard x:Name="sb" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="grid" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="00:00:00.5000000" Value="30">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CircleEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.4000000" Value="0"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="grid1" Storyboard.TargetProperty="(UIElement.RenderTransform).(CompositeTransform.Rotation)">
                <EasingDoubleKeyFrame KeyTime="00:00:00" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="00:00:00.5000000" Value="-30">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CircleEase EasingMode="EaseOut"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01" Value="0">
                    <EasingDoubleKeyFrame.EasingFunction>
                        <CubicEase EasingMode="EaseIn"/>
                    </EasingDoubleKeyFrame.EasingFunction>
                </EasingDoubleKeyFrame>
                <EasingDoubleKeyFrame KeyTime="00:00:01.4000000" Value="0"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </UserControl.Resources>
    <Grid Width="407.8" Height="378.9">
        <Grid x:Name="grid" RenderTransformOrigin="0.405,0.516">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Path Fill="#8A879B" Data="M187.6,181.8l134.2-83L188,176.2l-18.7-5.9l-6,3.7L187.6,181.8z">
            </Path>
            <Path Fill="#ACA7C1" Data="M187.6,181.8l134.2-83c5.7,9.1,2.8,21.2-6.3,26.8L177.7,211c-9.4,5.8-21.7,2.9-27.5-6.4L140,188.4&#xD;&#xA;		l23.3-14.4L187.6,181.8z">
            </Path>
            <Path Fill="#FF5A27" Data="M70.8,254.2l78.3-48.5c2.3-1.4,0.8-3.8,0-5c-2.6-4.4-5.6-8.6-9-12.4c-0.6-0.7-1.3-1.3-2.1-1.6&#xD;&#xA;		c-1.1-0.3-2.3,0.3-3.3,0.8c-8.8,4.5-17.5,9.2-26.3,13.7c-1.3,0.7-3.1,1.3-4.1,0.3c-0.6-0.6-0.7-1.4-0.8-2.2&#xD;&#xA;		c-1.7-15.1-13.7-29.5-28.8-30.5c-5.6-0.4-11.3,1-16.6,2.9c-10.2,3.6-19.7,8.7-28.2,15.4c-5.7,4.6-11.2,9.9-14.4,16.6&#xD;&#xA;		c-6.1,12.5-3.3,27.9,4.2,39.5c5,7.6,12.1,14.2,21,16.1C47.7,260.7,59.5,260.6,70.8,254.2z M70.1,233.2c-17,10.5-36.8,9.5-44.1-2.4&#xD;&#xA;		s0.5-30.1,17.5-40.6c17-10.5,36.8-9.5,44.1,2.4S87,222.7,70.1,233.2z">
            </Path>
            <Path Fill="#DD3C17" Data="M86.4,191c-0.3-0.6-0.7-1.2-1-1.7c-7.4-11.9-27.1-12.9-44.1-2.4c-17,10.6-24.8,28.7-17.5,40.6&#xD;&#xA;		c0.4,0.6,0.7,1.1,1.1,1.6c-5.6-11.9,2.3-28.8,18.5-38.8S78.3,180.8,86.4,191z">
            </Path>
            <Path Fill="#DD3C17" Data="M70.7,254.2l78.3-48.5c2.3-1.4,0.8-3.8,0-5c-0.6-0.9-1.1-1.9-1.8-2.9c0.7,1.3,1.8,3.3-0.3,4.6l-78.3,48.5&#xD;&#xA;		c-11.4,6.3-23.2,6.6-30,5c-8.5-1.8-15.4-7.9-20.3-15.1c0.5,0.9,0.9,1.6,1.5,2.4c5,7.6,12.1,14.2,21,16.1&#xD;&#xA;		C47.7,260.7,59.5,260.6,70.7,254.2z">
            </Path>
            <Path Opacity="0.3" Fill="#FFFFFF" Data="M23.4,195.3c-3.5,3.9-5.9,8.3-7.1,13.4&#xD;&#xA;		c-1.8,7.6-2.2,10.4-1.1,16.2c1.3,6.2,3.3,12.5,7.5,16.7c0.4,0.3,1.1,0.6,1.3,0.1c-2.4-5.1-4.2-10.6-5.4-16.2&#xD;&#xA;		c-0.9-4.6-1.4-9.2-0.4-13.6c0.5-2.3,1.3-4.4,2.3-6.4c1.6-3.3,3.6-6.4,6.1-9.1s5.6-5,7.1-8.4c-1-0.4-2.1,0.1-3,0.8&#xD;&#xA;		C28,190.5,25.5,192.7,23.4,195.3z">
            </Path>
            <Path Opacity="0.3" Fill="#FFFFFF" Data="M103.1,203.8c2.2,2.7,5.9,3.8,9.3,3.5s6.7-1.7,9.7-3.4&#xD;&#xA;		c3-1.6,6.1-3.3,9.3-4.3c0.8-0.2,1.7-0.4,2.5-0.3c1.3,0.3,2.4,1.2,3.6,1.9c1.1,0.7,2.7,1.2,3.9,0.4c1.6-1.1,1-3.5,0.3-5.2&#xD;&#xA;		c-0.8-1.9-1.9-3.8-3.8-4.7c-2.7-1.3-5.8,0.3-8.4,1.7c-4.4,2.4-8.8,5-13.1,7.4C113.5,202.5,106,207.5,103.1,203.8z">
            </Path>
            <Path Fill="#C62F17" Data="M150,204.2l-0.2-0.5L69.6,255l79.6-48.2C150.1,206.3,150.4,205.1,150,204.2z">
            </Path>
        </Grid>
        <Grid x:Name="grid1" RenderTransformOrigin="0.406,0.515">
            <Grid.RenderTransform>
                <CompositeTransform/>
            </Grid.RenderTransform>
            <Path Fill="#8A879B" Data="M184,207.3l6-3.7l3-19.4l128.8-85.3l-134,83.2L184,207.3z">
            </Path>
            <Path Fill="#C7C3E0" Data="M294.9,92.6c9.2-5.7,21.2-2.9,26.9,6.3l-134,83.2l-3.8,25.2l-23.3,14.4l-9.5-15.3&#xD;&#xA;		c-6.1-9.9-3.1-22.9,6.8-29.1L294.9,92.6z">
            </Path>
            <Path Opacity="0.44" Fill="#FFFFFF" Data="M269.2,112.2c-23.1,14.3-57.9,34.5-80.6,50.5&#xD;&#xA;		c26.9-15.5,64.3-38.5,90.9-53.7c7.1-4.1,14.9-8.2,22.4-7.3c2.4,0.3,4.6,1,7,1s5.2-1.2,6.2-3.4c1.1-2.8-1.4-5.3-4.2-6&#xD;&#xA;		c-7-1.8-14.8,2.5-21.5,6.6C282.7,104,276,108.1,269.2,112.2z">
            </Path>
            <Path Fill="#FF5A27" Data="M52.9,278.8c-2.2,8.7,0.5,18.2,5.1,25.9c7.1,11.9,19.7,21.2,33.5,21.4c7.4,0.1,14.6-2.4,21.3-5.5&#xD;&#xA;		c9.7-4.6,18.6-10.8,26.3-18.3c4.1-4,7.8-8.4,10-13.6c5.8-14-1.8-31-14.5-39.4c-0.6-0.4-1.4-0.8-1.6-1.6c-0.5-1.4,0.9-2.7,2-3.5&#xD;&#xA;		c8-5.7,16-11.6,24.1-17.4c1-0.7,1.9-1.4,2.2-2.7c0.2-0.9-0.2-1.8-0.5-2.6c-1.9-4.8-4.3-9.3-7.1-13.6c-0.8-1.2-2.2-3.6-4.5-2.2&#xD;&#xA;		l-78.3,48.5C60.1,261.5,54.7,272,52.9,278.8z M133.4,266.6c7.3,11.9-0.5,30.1-17.5,40.6s-36.8,9.5-44.1-2.4&#xD;&#xA;		c-7.3-11.9,0.5-30.1,17.5-40.6C106.3,253.7,126,254.8,133.4,266.6z">
            </Path>
            <Path Fill="#DD3C17" Data="M134.4,268.4c0.4,0.5,0.9,1,1.1,1.6c7.3,11.9-0.5,30.1-17.5,40.6s-36.8,9.5-44.1-2.4&#xD;&#xA;		c-0.4-0.6-0.7-1.2-1-1.7c8.1,10.4,26.8,10.8,43,0.8C132.1,297.2,139.9,280.3,134.4,268.4z">
            </Path>
            <Path Fill="#DD3C17" Data="M70.8,254.2l78.3-48.5c2.3-1.4,3.7,1,4.5,2.2c0.6,0.9,1.2,1.9,1.8,2.9c-0.8-1.2-2.2-3-4.2-1.8l-78.3,48.5&#xD;&#xA;		c-10.8,7.3-16.2,17.7-18,24.6c-2.1,8.4,0.3,17.3,4.6,24.9c-0.5-0.8-1.1-1.6-1.5-2.3c-4.7-7.8-7.4-17.2-5.1-25.9&#xD;&#xA;		C54.7,272,60.1,261.5,70.8,254.2z">
            </Path>
            <Path Opacity="0.3" Fill="#FFFFFF" Data="M56.8,286.3c-0.7,5.4,0.1,11,2.2,16.1&#xD;&#xA;		c2.4,5.5,7.2,9.1,11.4,13.3c0.1,0.1,0.2,0.2,0.4,0.3c0.4,0.1,0.7-0.3,0.7-0.8c0-0.4-0.3-0.7-0.6-1c-3.7-4-6.8-8.7-8.4-13.8&#xD;&#xA;		c-1.7-5.1-2-10.8-0.3-16c1.5-4.8,4.5-8.9,7.9-12.7c4.6-5,9.8-9.3,15.6-12.9c4.2-2.6,8.7-4.8,12.7-7.7c0.8-0.5,1.4-1,1.9-1.8&#xD;&#xA;		c0.4-0.7,0.5-1.7,0-2.4c-0.9-1.2-2.7-0.5-4.1,0.1c-7.1,3.4-13.8,7.5-20.2,12.2c-6.8,4.9-13.1,10.8-16.6,18.4&#xD;&#xA;		C57.6,278.6,57.1,284.5,56.8,286.3z">
            </Path>
            <Path Fill="#8A879B" Data="M154.5,202.4c-3.8-6.2-1.9-14.3,4.2-18.1c6.2-3.8,14.3-1.9,18.1,4.2c3.8,6.2,1.9,14.3-4.2,18.1&#xD;&#xA;		C166.5,210.4,158.5,208.5,154.5,202.4z">
            </Path>
            <Path Fill="#C7C3E0" Data="M156.2,201.2c-3.2-5.3-1.6-12.2,3.7-15.4s12.2-1.6,15.4,3.7s1.6,12.2-3.7,15.4S159.5,206.6,156.2,201.2z">
            </Path>
            <Path Fill="#8A879B" Data="M169.1,184.6l-3.7,23l-2.6-0.4l4.1-23L169.1,184.6z">
            </Path>
        </Grid>
    </Grid>
</UserControl>